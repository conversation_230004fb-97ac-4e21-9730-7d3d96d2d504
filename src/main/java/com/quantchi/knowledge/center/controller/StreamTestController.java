package com.quantchi.knowledge.center.controller;

import com.quantchi.knowledge.center.bean.system.vo.AiReportStreamResponse;
import com.quantchi.knowledge.center.util.StreamDiagnosticUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.time.Duration;

/**
 * 流式接口测试控制器
 * 用于测试流式响应是否正常工作
 * 
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Api(tags = "流式接口测试")
@RequiredArgsConstructor
public class StreamTestController {

    private final StreamDiagnosticUtil streamDiagnosticUtil;

    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("测试流式响应")
    public Flux<ServerSentEvent<AiReportStreamResponse>> testStream() {
        log.info("开始测试流式响应");
        
        return Flux.interval(Duration.ofSeconds(1))
                .take(5)
                .map(i -> {
                    AiReportStreamResponse response = new AiReportStreamResponse();
                    response.setEvent("message");
                    response.setTaskId("test-task-" + i);
                    response.setMessageId("test-msg-" + i);
                    response.setConversationId("test-conv");
                    response.setAnswer("这是第 " + (i + 1) + " 条测试消息");
                    response.setIsEnd(i == 4);
                    response.setCreatedAt(System.currentTimeMillis());
                    
                    if (i == 4) {
                        response.setEvent("message_end");
                        response.setAnswer("");
                        response.setReportId(12345L);
                        response.setFullContent("完整的测试内容");
                    }
                    
                    log.info("发送测试消息: {}", i + 1);
                    return response;
                })
                .map(response -> ServerSentEvent.<AiReportStreamResponse>builder()
                        .data(response)
                        .event(response.getEvent())
                        .id(response.getMessageId())
                        .build())
                .doOnComplete(() -> log.info("测试流式响应完成"))
                .doOnError(error -> log.error("测试流式响应错误", error));
    }
    
    @GetMapping(value = "/simple-stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("简单流式响应测试")
    public Flux<String> testSimpleStream() {
        log.info("开始简单流式响应测试");

        return Flux.interval(Duration.ofSeconds(1))
                .take(3)
                .map(i -> "data: 测试消息 " + (i + 1) + "\n\n")
                .doOnComplete(() -> log.info("简单流式响应测试完成"));
    }

    @GetMapping("/diagnose")
    @ApiOperation("诊断流式接口配置")
    public String diagnoseConfiguration() {
        log.info("开始诊断流式接口配置");
        streamDiagnosticUtil.checkWebFluxConfiguration();
        streamDiagnosticUtil.testExternalApiConnectivity();
        return "诊断完成，请查看日志";
    }

    @GetMapping(value = "/simulate-sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("模拟外部API的SSE响应")
    public Flux<String> simulateSSE() {
        return streamDiagnosticUtil.simulateExternalApiResponse();
    }

    @GetMapping(value = "/simulate-real-api", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("模拟真实外部API的响应格式")
    public Flux<String> simulateRealApiResponse() {
        log.info("开始模拟真实外部API响应格式");

        return Flux.just(
                "{\"event\": \"message\", \"conversation_id\": \"test-conv\", \"message_id\": \"msg-1\", \"created_at\": 1751254025, \"task_id\": \"test-task\", \"id\": \"msg-1\", \"answer\": \"这是第一段内容\", \"from_variable_selector\": [\"1749805133562\", \"answer\"]}",
                "{\"event\": \"message\", \"conversation_id\": \"test-conv\", \"message_id\": \"msg-2\", \"created_at\": 1751254026, \"task_id\": \"test-task\", \"id\": \"msg-2\", \"answer\": \"这是第二段内容\", \"from_variable_selector\": [\"1749805133562\", \"answer\"]}",
                "{\"event\": \"message\", \"conversation_id\": \"test-conv\", \"message_id\": \"msg-3\", \"created_at\": 1751254027, \"task_id\": \"test-task\", \"id\": \"msg-3\", \"answer\": \"这是第三段内容\", \"from_variable_selector\": [\"1749805133562\", \"answer\"]}",
                "{\"event\": \"message_end\", \"conversation_id\": \"test-conv\", \"message_id\": \"msg-end\", \"created_at\": 1751254028, \"task_id\": \"test-task\", \"id\": \"msg-end\", \"answer\": \"\"}"
        )
        .delayElements(Duration.ofSeconds(1))
        .doOnNext(data -> log.info("发送模拟数据: {}", data.substring(0, Math.min(50, data.length())) + "..."))
        .doOnComplete(() -> log.info("模拟真实API响应完成"));
    }
}
