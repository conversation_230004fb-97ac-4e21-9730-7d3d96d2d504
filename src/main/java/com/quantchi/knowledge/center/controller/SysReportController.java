package com.quantchi.knowledge.center.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.github.pagehelper.PageInfo;
import com.quantchi.knowledge.center.bean.enums.BusinessType;
import com.quantchi.knowledge.center.bean.enums.ReportThemeEnum;
import com.quantchi.knowledge.center.bean.exception.BusinessException;
import com.quantchi.knowledge.center.bean.system.bo.*;
import com.quantchi.knowledge.center.bean.system.query.ReportQuery;
import com.quantchi.knowledge.center.bean.system.vo.AiReportDetailVO;
import com.quantchi.knowledge.center.bean.system.vo.AiReportStreamResponse;
import com.quantchi.knowledge.center.bean.system.vo.MaterialSearchVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportBatchDeleteResultVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportDetailVO;
import com.quantchi.knowledge.center.bean.system.vo.SysReportPageVO;
import com.quantchi.knowledge.center.bean.vo.TocEntryVO;
import com.quantchi.knowledge.center.common.ResultConvert;
import com.quantchi.knowledge.center.common.ResultInfo;
import com.quantchi.knowledge.center.config.annotation.Log;
import com.quantchi.knowledge.center.service.IMaterialSearchService;
import com.quantchi.knowledge.center.service.ISysAiReportService;
import com.quantchi.knowledge.center.service.ISysReportService;
import com.quantchi.knowledge.center.service.ISysUserApiUsageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 报告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/sys/report")
@Api(tags = "报告中心接口")
public class SysReportController {

    private final ISysReportService sysReportService;
    private final ISysUserApiUsageService sysUserApiUsageService;
    private final ISysAiReportService sysAiReportService;
    private final IMaterialSearchService materialSearchService;

    @PostMapping("/upload")
    @ApiOperation("上传报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.UPLOAD)
    public ResultInfo<Boolean> uploadReport(@Valid @RequestBody final SysReportUploadBO reportUploadBO) throws Exception {
        return ResultConvert.success(sysReportService.uploadReport(reportUploadBO));
    }

    @PostMapping("/myReportList")
    @ApiOperation("我的报告列表")
    public ResultInfo<PageInfo<SysReportPageVO>> myReportList(@RequestBody final ReportQuery reportQuery) {
        return ResultConvert.success(sysReportService.myReportList(reportQuery));
    }

    @PostMapping("/reportMarket")
    @ApiOperation("报告集市")
    @Log(title = "报告中心-报告集市", businessType = BusinessType.VIEW)
    public ResultInfo<PageInfo<SysReportPageVO>> reportMarket(@RequestBody final ReportQuery reportQuery) {
        return ResultConvert.success(sysReportService.reportMarket(reportQuery));
    }

    @PostMapping("/public")
    @ApiOperation("公开报告")
    public ResultInfo<Boolean> publicReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.publicReport(reportId));
    }

    @PostMapping("/unShelf")
    @ApiOperation("下架报告")
    public ResultInfo<Boolean> unShelfReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.unShelfReport(reportId));
    }

    @PostMapping("/delete")
    @ApiOperation("删除报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.DELETE)
    public ResultInfo<Boolean> deleteReport(@RequestParam final Long reportId) {
        return ResultConvert.success(sysReportService.deleteReport(reportId));
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.DELETE)
    public ResultInfo<SysReportBatchDeleteResultVO> batchDeleteReport(@Valid @RequestBody final SysReportBatchDeleteBO batchDeleteBO) {
        return ResultConvert.success(sysReportService.batchDeleteReport(batchDeleteBO));
    }

    @PostMapping("/info")
    @ApiOperation("报告详情")
    @SaCheckLogin
    public ResultInfo<SysReportDetailVO> reportInfo(@RequestParam final Long reportId) {
        Long userId = StpUtil.getLoginIdAsLong();
        
        // 检查用户是否有权限查看报告
        boolean canUseApi = sysUserApiUsageService.checkUserCanUseApi(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        if (!canUseApi) {
            throw new BusinessException("您的免费查看报告次数已用完，请升级为正式账号继续使用");
        }
        
        // 记录API使用情况
        sysUserApiUsageService.recordApiUsage(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        return ResultConvert.success(sysReportService.reportInfo(reportId));
    }
    
    @GetMapping("/usage/remaining")
    @ApiOperation("获取报告查看剩余次数")
    @SaCheckLogin
    public ResultInfo<Integer> getRemainingReportUsage() {
        Long userId = StpUtil.getLoginIdAsLong();
        int remainingCount = sysUserApiUsageService.getRemainingUsageCount(userId, ISysUserApiUsageService.API_TYPE_REPORT);
        return ResultConvert.success(remainingCount);
    }

    @PostMapping("/submit")
    @ApiOperation("提交报告")
    @Log(title = "工作台-我的报告", businessType = BusinessType.UPDATE)
    public ResultInfo<Boolean> submitReport(@Valid @RequestBody final SysReportSubmitBO reportSubmitBO) {
        return ResultConvert.success(sysReportService.submitReport(reportSubmitBO));
    }

    @PostMapping("/download")
    @ApiOperation("下载报告")
    @Log(title = "报告中心-报告下载", businessType = BusinessType.DOWNLOAD)
    public ResultInfo<Integer> downloadReport(@Valid @RequestBody final SysReportDownloadBO reportDownloadBO) throws Exception {
        return ResultConvert.success(sysReportService.downloadReport(reportDownloadBO));
    }

    // ==================== AI报告相关接口 ====================

    @PostMapping("/ai/generateOutline")
    @ApiOperation("生成报告大纲")
    @Log(title = "工作台-AI报告", businessType = BusinessType.UPDATE)
    public ResultInfo<List<TocEntryVO>> generateOutline(@Valid @RequestBody final AiReportCreateBO createBO) {
        return ResultConvert.success(sysAiReportService.generateOutline(createBO));
    }

    @PostMapping("/ai/generate")
    @ApiOperation("生成AI报告")
    @Log(title = "工作台-AI报告", businessType = BusinessType.INSERT)
    public ResultInfo<String> generateAiReport(@Valid @RequestBody final AiReportGenerateBO generateBO) {
        return ResultConvert.success(sysAiReportService.generateAiReport(generateBO));
    }

    @PostMapping(value = "/ai/generateStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("流式生成AI报告")
    @SaCheckLogin
    @Log(title = "工作台-AI报告", businessType = BusinessType.INSERT)
    public Flux<ServerSentEvent<AiReportStreamResponse>> generateAiReportStream(@Valid @RequestBody final AiReportGenerateBO generateBO) {
        log.info("开始流式生成AI报告，标题: {}", generateBO.getTitle());

        return sysAiReportService.generateAiReportStream(generateBO)
                .doOnSubscribe(subscription -> log.info("客户端订阅流式响应"))
                .doOnNext(response -> log.info("准备发送响应: event={}, answer长度={}",
                         response.getEvent(),
                         response.getAnswer() != null ? response.getAnswer().length() : 0))
                .map(response -> {
                    ServerSentEvent<AiReportStreamResponse> sse = ServerSentEvent.<AiReportStreamResponse>builder()
                            .data(response)
                            .event(response.getEvent())
                            .id(response.getMessageId())
                            .build();
                    log.debug("创建SSE事件: event={}, id={}", response.getEvent(), response.getMessageId());
                    return sse;
                })
                .doOnNext(sse -> log.info("发送SSE事件到客户端: event={}", sse.event()))
                .doOnComplete(() -> log.info("流式响应完成"))
                .doOnError(error -> log.error("流式响应错误", error))
                .doOnCancel(() -> log.warn("客户端取消了流式响应"));
    }

    @GetMapping("/ai/detail/{reportId}")
    @ApiOperation("获取AI报告详情")
    public ResultInfo<AiReportDetailVO> getAiReportDetail(@PathVariable final Long reportId) {
        return ResultConvert.success(sysAiReportService.getAiReportDetail(reportId));
    }

    @GetMapping("/ai/themes")
    @ApiOperation("获取AI报告主题选项")
    public ResultInfo<String[]> getReportThemes() {
        return ResultConvert.success(ReportThemeEnum.getAllThemes());
    }

    @PostMapping("/ai/materialSearch")
    @ApiOperation("素材检索")
    @Log(title = "工作台-AI报告", businessType = BusinessType.QUERY)
    public ResultInfo<MaterialSearchVO> materialSearch(@Valid @RequestBody final MaterialSearchBO searchBO) {
        // 如果用户ID为空，使用当前登录用户ID
        if (CharSequenceUtil.isBlank(searchBO.getUserId())) {
            searchBO.setUserId(String.valueOf(StpUtil.getLoginIdAsLong()));
        }

        return ResultConvert.success(materialSearchService.materialSearch(searchBO));
    }

}
