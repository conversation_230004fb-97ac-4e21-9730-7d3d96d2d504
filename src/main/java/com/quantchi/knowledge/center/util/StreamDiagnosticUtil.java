package com.quantchi.knowledge.center.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.time.Duration;

/**
 * 流式接口诊断工具
 * 用于测试和诊断流式接口的问题
 * 
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@Component
public class StreamDiagnosticUtil {

    /**
     * 测试外部API的连通性
     */
    public void testExternalApiConnectivity() {
        final String url = "https://tianying-dev.supxmind.quant-chi.com/v1/chat-messages";
        
        log.info("开始测试外部API连通性: {}", url);
        
        WebClient client = WebClient.builder().build();
        
        try {
            String response = client.get()
                    .uri(url.replace("/v1/chat-messages", "/health"))
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();
            
            log.info("外部API连通性测试成功: {}", response);
        } catch (Exception e) {
            log.error("外部API连通性测试失败", e);
        }
    }
    
    /**
     * 测试简单的流式响应
     */
    public Flux<String> testSimpleStream() {
        log.info("开始测试简单流式响应");
        
        return Flux.interval(Duration.ofSeconds(1))
                .take(3)
                .map(i -> "测试消息 " + (i + 1))
                .doOnNext(msg -> log.info("生成测试消息: {}", msg))
                .doOnComplete(() -> log.info("简单流式响应测试完成"))
                .doOnError(error -> log.error("简单流式响应测试失败", error));
    }
    
    /**
     * 模拟外部API的SSE响应
     */
    public Flux<String> simulateExternalApiResponse() {
        log.info("开始模拟外部API的SSE响应");
        
        return Flux.just(
                "data: {\"event\":\"message\",\"task_id\":\"test-123\",\"message_id\":\"msg-1\",\"conversation_id\":\"conv-1\",\"answer\":\"这是第一条消息\"}\n\n",
                "data: {\"event\":\"message\",\"task_id\":\"test-123\",\"message_id\":\"msg-2\",\"conversation_id\":\"conv-1\",\"answer\":\"这是第二条消息\"}\n\n",
                "data: {\"event\":\"message\",\"task_id\":\"test-123\",\"message_id\":\"msg-3\",\"conversation_id\":\"conv-1\",\"answer\":\"这是第三条消息\"}\n\n",
                "data: {\"event\":\"message_end\",\"task_id\":\"test-123\",\"message_id\":\"msg-end\",\"conversation_id\":\"conv-1\",\"answer\":\"\"}\n\n"
        )
        .delayElements(Duration.ofSeconds(1))
        .doOnNext(data -> log.info("模拟发送SSE数据: {}", data.trim()))
        .doOnComplete(() -> log.info("模拟SSE响应完成"));
    }
    
    /**
     * 检查WebFlux配置
     */
    public void checkWebFluxConfiguration() {
        log.info("检查WebFlux配置...");
        
        // 检查是否有WebFlux依赖
        try {
            Class.forName("org.springframework.web.reactive.function.client.WebClient");
            log.info("✓ WebClient类可用");
        } catch (ClassNotFoundException e) {
            log.error("✗ WebClient类不可用，请检查WebFlux依赖");
        }
        
        try {
            Class.forName("reactor.core.publisher.Flux");
            log.info("✓ Flux类可用");
        } catch (ClassNotFoundException e) {
            log.error("✗ Flux类不可用，请检查Reactor依赖");
        }
        
        try {
            Class.forName("org.springframework.http.codec.ServerSentEvent");
            log.info("✓ ServerSentEvent类可用");
        } catch (ClassNotFoundException e) {
            log.error("✗ ServerSentEvent类不可用，请检查Spring WebFlux依赖");
        }
    }
}
